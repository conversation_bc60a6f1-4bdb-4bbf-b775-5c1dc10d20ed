#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟漏洞扫描系统完整示例
演示从登录到查询任务再到漏洞查询的完整流程
"""

from login_manager import GreenLeagueLogin
from task_manager import TaskManager
from vuln_manager import VulnManager

def example_complete_workflow():
    """完整工作流程示例"""
    print("=" * 80)
    print("绿盟漏洞扫描系统 - 完整工作流程示例")
    print("=" * 80)
    
    # 步骤1: 登录系统
    print("\n步骤1: 登录系统")
    print("-" * 40)
    
    login_manager = GreenLeagueLogin(host="************")
    success, login_result = login_manager.auto_login()
    
    if not success:
        print("登录失败，无法继续后续操作")
        return
    
    # 步骤2: 初始化任务和漏洞管理器
    print("\n步骤2: 初始化管理器")
    print("-" * 40)

    session = login_manager.get_session()
    base_url = login_manager.get_base_url()  # 这现在返回HTTP URL用于API调用

    task_manager = TaskManager(session, base_url)
    vuln_manager = VulnManager(session, base_url)

    print("✓ 任务管理器初始化完成")
    print("✓ 漏洞管理器初始化完成")
    print(f"  使用API URL: {base_url}")
    
    # 步骤3: 查询任务统计
    print("\n步骤3: 查询任务统计")
    print("-" * 40)
    
    task_manager.get_task_statistics()
    
    # 步骤4: 获取已完成的任务
    print("\n步骤4: 获取已完成的任务")
    print("-" * 40)
    
    success, completed_tasks = task_manager.get_completed_tasks()
    
    if not success or not completed_tasks:
        print("没有找到已完成的任务")
        return
    
    # 显示已完成任务摘要
    task_manager.display_task_summary(completed_tasks)
    
    # 步骤5: 分析已完成任务的原理扫描漏洞
    print("\n步骤5: 分析原理扫描漏洞")
    print("-" * 40)
    
    # 提取任务ID列表
    task_ids = [task['task_id'] for task in completed_tasks]
    
    # 分析多个任务的原理扫描漏洞
    analysis_result = vuln_manager.analyze_multiple_tasks_vulns(
        task_ids, 
        focus_on_principle=True
    )
    
    # 步骤6: 显示详细的漏洞信息
    print("\n步骤6: 显示详细漏洞信息")
    print("-" * 40)
    
    all_principle_vulns = analysis_result['all_vulns']
    
    if all_principle_vulns:
        # 显示前10个漏洞的详细信息
        vuln_manager.display_vuln_details(
            all_principle_vulns[:10], 
            "原理扫描漏洞详情 (前10个)"
        )
        
        # 显示完整统计
        vuln_manager.display_vuln_statistics(
            all_principle_vulns, 
            "原理扫描漏洞统计"
        )
    else:
        print("没有发现原理扫描漏洞")
    
    print("\n" + "=" * 80)
    print("完整工作流程执行完成！")
    print("=" * 80)

# def example_single_task_analysis():
#     """单个任务分析示例"""
#     print("=" * 80)
#     print("单个任务详细分析示例")
#     print("=" * 80)
    
#     # 登录
#     login_manager = GreenLeagueLogin(host="************")
#     success, login_result = login_manager.auto_login()
    
#     if not success:
#         print("登录失败")
#         return
    
#     # 初始化管理器
#     session = login_manager.get_session()
#     base_url = login_manager.get_base_url()
#     task_manager = TaskManager(session, base_url)
#     vuln_manager = VulnManager(session, base_url)
    
#     # 获取已完成的任务
#     success, completed_tasks = task_manager.get_completed_tasks()
    
#     if not success or not completed_tasks:
#         print("没有找到已完成的任务")
#         return
    
#     # 选择第一个任务进行详细分析
#     first_task = completed_tasks[0]
#     task_id = first_task['task_id']
#     task_name = first_task.get('task_name', '未知任务')
    
#     print(f"\n正在分析任务: {task_name} (ID: {task_id})")
#     print("-" * 60)
    
#     # 获取所有漏洞
#     success, all_vulns = vuln_manager.get_all_vulns_for_task(task_id)
    
#     if success and all_vulns:
#         print(f"\n任务 {task_id} 的所有漏洞:")
#         vuln_manager.display_vuln_statistics(all_vulns, f"任务 {task_id} 漏洞统计")
        
#         # 过滤原理扫描漏洞
#         principle_vulns = vuln_manager.filter_principle_scan_vulns(all_vulns)
        
#         if principle_vulns:
#             print(f"\n任务 {task_id} 的原理扫描漏洞:")
#             vuln_manager.display_vuln_details(principle_vulns, "原理扫描漏洞详情")
#         else:
#             print(f"\n任务 {task_id} 没有发现原理扫描漏洞")
#     else:
#         print(f"无法获取任务 {task_id} 的漏洞信息")

# def example_custom_analysis():
#     """自定义分析示例"""
#     print("=" * 80)
#     print("自定义分析示例")
#     print("=" * 80)
    
#     # 登录
#     login_manager = GreenLeagueLogin(host="************")
#     success, login_result = login_manager.auto_login()
    
#     if not success:
#         print("登录失败")
#         return
    
#     # 初始化管理器
#     session = login_manager.get_session()
#     base_url = login_manager.get_base_url()
#     task_manager = TaskManager(session, base_url)
#     vuln_manager = VulnManager(session, base_url)
    
#     # 获取所有任务
#     success, all_tasks = task_manager.get_all_tasks()
    
#     if not success:
#         print("获取任务列表失败")
#         return
    
#     print(f"\n系统中共有 {len(all_tasks)} 个任务")
    
#     # 显示任务统计
#     task_manager.get_task_statistics()
    
#     # 搜索特定任务
#     print("\n搜索包含关键字的任务:")
#     success, matched_tasks = task_manager.search_tasks_by_name("扫描")
    
#     if success and matched_tasks:
#         task_manager.display_task_summary(matched_tasks[:5])  # 显示前5个匹配的任务
    
#     # 分析不同状态的任务
#     print("\n分析不同状态的任务:")
#     for status in [15, 10, 5, 0]:  # 已完成、运行中、等待中、未开始
#         success, status_tasks = task_manager.get_tasks_by_status(status)
#         if success and status_tasks:
#             print(f"状态 {status} 的任务数量: {len(status_tasks)}")

# def main():
#     """主函数 - 选择运行不同的示例"""
#     print("绿盟漏洞扫描系统示例程序")
#     print("=" * 50)
#     print("请选择要运行的示例:")
#     print("1. 完整工作流程示例")
#     print("2. 单个任务分析示例")
#     print("3. 自定义分析示例")
#     print("4. 运行所有示例")
    
#     try:
#         choice = input("\n请输入选择 (1-4): ").strip()
        
#         if choice == "1":
#             example_complete_workflow()
#         elif choice == "2":
#             example_single_task_analysis()
#         elif choice == "3":
#             example_custom_analysis()
#         elif choice == "4":
#             example_complete_workflow()
#             print("\n" + "="*100 + "\n")
#             example_single_task_analysis()
#             print("\n" + "="*100 + "\n")
#             example_custom_analysis()
#         else:
#             print("无效选择，运行默认示例...")
#             example_complete_workflow()
            
#     except KeyboardInterrupt:
#         print("\n\n程序被用户中断")
#     except Exception as e:
#         print(f"\n程序执行出错: {str(e)}")

if __name__ == "__main__":
    # 默认运行完整工作流程示例
    example_complete_workflow()
    
    # 如果需要交互式选择，取消下面的注释
    # main()
