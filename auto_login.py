#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟自动登录脚本
根据CSV文件中的登录流程实现自动化登录
"""

import requests
import json
import base64
import ddddocr
from io import BytesIO
import time

class GreenLeagueLogin:
    def __init__(self, host="************"):
        self.host = host
        self.base_url = f"https://{host}"
        self.session = requests.Session()

        # 禁用SSL证书验证（因为服务器使用自签名证书）
        self.session.verify = False

        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'https://{host}/'
        })
        
        # 初始化验证码识别器
        self.ocr = ddddocr.DdddOcr()
    
    def get_captcha(self):
        """
        获取验证码图片和identifier
        返回: (captcha_image_data, identifier)
        """
        try:
            url = f"{self.base_url}/interface/myauth/captcha/"
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 获取base64编码的图片数据和identifier
                    image_base64 = data['data']['mg_str']['image']
                    identifier = data['data']['identifier']
                    
                    # 解码base64图片
                    image_data = base64.b64decode(image_base64)
                    
                    print(f"✓ 成功获取验证码，identifier: {identifier}")
                    return image_data, identifier
                else:
                    print(f"✗ 获取验证码失败: {data.get('message', '未知错误')}")
                    return None, None
            else:
                print(f"✗ 请求验证码失败，状态码: {response.status_code}")
                return None, None
                
        except Exception as e:
            print(f"✗ 获取验证码异常: {str(e)}")
            return None, None
    
    def recognize_captcha(self, image_data):
        """
        识别验证码
        参数: image_data - 图片二进制数据
        返回: 识别结果字符串
        """
        try:
            result = self.ocr.classification(image_data)
            print(f"✓ 验证码识别结果: {result}")
            return result
        except Exception as e:
            print(f"✗ 验证码识别失败: {str(e)}")
            return None
    
    def login(self, username, password, captcha_code, identifier):
        """
        执行登录
        参数:
            username: 用户名
            password: 密码（已加密）
            captcha_code: 验证码
            identifier: 验证码标识符
        """
        try:
            url = f"{self.base_url}/interface/myauth/login"
            
            # 构造登录数据
            login_data = {
                "username": username,
                "password": password,
                "captcha_code": captcha_code,
                "identifier": identifier
            }
            
            # 设置Content-Type
            headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': f'https://{self.host}'
            }
            
            response = self.session.post(url, json=login_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    print(f"✓ 登录成功: {data.get('message', '登录成功')}")
                    
                    # 提取重要信息
                    user_info = data.get('data', {})
                    token = user_info.get('token')
                    username = user_info.get('user_info', {}).get('username')
                    group_name = user_info.get('user_info', {}).get('group_name')
                    
                    print(f"  用户名: {username}")
                    print(f"  用户组: {group_name}")
                    print(f"  Token: {token[:50]}..." if token else "  Token: 未获取到")
                    
                    return True, data
                else:
                    print(f"✗ 登录失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                print(f"✗ 登录请求失败，状态码: {response.status_code}")
                return False, None
                
        except Exception as e:
            print(f"✗ 登录异常: {str(e)}")
            return False, None
    
    def auto_login(self, username="admin", password="U2FsdGVkX18pUgexLHc5Y8RrbtAlXz+3EZrDpYJqFqE=", max_retries=5):
        """
        自动登录流程
        参数:
            username: 用户名，默认为admin
            password: 加密后的密码
            max_retries: 最大重试次数
        """
        print("=" * 50)
        print("开始绿盟自动登录流程")
        print("=" * 50)
        
        for attempt in range(max_retries):
            print(f"\n第 {attempt + 1} 次尝试登录...")
            
            # 步骤1: 获取验证码
            print("1. 获取验证码...")
            image_data, identifier = self.get_captcha()
            
            if not image_data or not identifier:
                print("获取验证码失败，重试...")
                time.sleep(1)
                continue
            
            # 步骤2: 识别验证码
            print("2. 识别验证码...")
            captcha_code = self.recognize_captcha(image_data)
            
            if not captcha_code:
                print("验证码识别失败，重试...")
                time.sleep(1)
                continue
            
            # 步骤3: 执行登录
            print("3. 执行登录...")
            success, result = self.login(username, password, captcha_code, identifier)
            
            if success:
                print("\n" + "=" * 50)
                print("登录成功！")
                print("=" * 50)
                return True, result
            else:
                print(f"登录失败，{max_retries - attempt - 1} 次重试机会剩余")
                time.sleep(2)
        
        print("\n" + "=" * 50)
        print("登录失败，已达到最大重试次数")
        print("=" * 50)
        return False, None

    def get_task_list(self, page=1, page_size=100):
        """
        获取任务列表
        参数:
            page: 页码，默认为1
            page_size: 每页大小，默认为100
        返回: (success, task_list_data)
        """
        try:
            url = f"{self.base_url}/interface/task/task_list/"

            # 构造请求数据
            request_data = {
                "page": page,
                "page_size": page_size
            }

            # 设置请求头
            headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': f'https://{self.host}'
            }

            response = self.session.post(url, json=request_data, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    print(f"✓ 成功获取任务列表，共 {len(data.get('data', {}).get('results', []))} 个任务")
                    return True, data
                else:
                    print(f"✗ 获取任务列表失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                print(f"✗ 请求任务列表失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            print(f"✗ 获取任务列表异常: {str(e)}")
            return False, None

    def get_completed_tasks(self):
        """
        获取已完成的任务（task_status = 15）
        返回: (success, completed_tasks)
        """
        print("正在查询已完成的任务...")

        success, task_data = self.get_task_list()
        if not success:
            return False, []

        # 提取已完成的任务
        all_tasks = task_data.get('data', {}).get('results', [])
        completed_tasks = [task for task in all_tasks if task.get('task_status') == 15]

        print(f"✓ 找到 {len(completed_tasks)} 个已完成的任务")

        # 打印已完成任务的基本信息
        for task in completed_tasks:
            task_id = task.get('task_id')
            task_name = task.get('task_name', '未知任务')
            create_time = task.get('create_time', '未知时间')
            print(f"  - 任务ID: {task_id}, 任务名称: {task_name}, 创建时间: {create_time}")

        return True, completed_tasks

    def get_vuln_distribution(self, task_id, page=1, size=100):
        """
        获取指定任务的漏洞分布信息
        参数:
            task_id: 任务ID
            page: 页码，默认为1
            size: 每页大小，默认为100
        返回: (success, vuln_data)
        """
        try:
            # 构造URL，包含查询参数
            url = f"{self.base_url}/interface/report/sys/vuln-distribution/{task_id}"
            params = {
                'task_ids': task_id,
                'source': 'online',
                'page': page,
                'size': size,
                'filterVulnLevels': 'high,middle,low',
                'vul_category_id': ''
            }

            response = self.session.get(url, params=params)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    vuln_info = data.get('data', {}).get('vulns_info', {})
                    vuln_list = vuln_info.get('vuln_distribution', {}).get('vuln_list', [])
                    print(f"✓ 成功获取任务 {task_id} 的漏洞信息，共 {len(vuln_list)} 个漏洞")
                    return True, data
                else:
                    print(f"✗ 获取任务 {task_id} 漏洞信息失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                print(f"✗ 请求任务 {task_id} 漏洞信息失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            print(f"✗ 获取任务 {task_id} 漏洞信息异常: {str(e)}")
            return False, None

    def filter_principle_scan_vulns(self, vuln_list):
        """
        过滤出原理扫描的漏洞
        参数:
            vuln_list: 漏洞列表
        返回: 原理扫描漏洞列表
        """
        principle_vulns = []

        for vuln in vuln_list:
            vuln_name = vuln.get('i18n_name', '')
            # 检查漏洞名称是否包含"原理扫描"标识
            if '原理扫描' in vuln_name or '【原理扫描】' in vuln_name:
                principle_vulns.append(vuln)

        return principle_vulns

    def analyze_completed_tasks_vulns(self):
        """
        分析已完成任务的原理扫描漏洞
        """
        print("\n" + "=" * 60)
        print("开始分析已完成任务的原理扫描漏洞")
        print("=" * 60)

        # 1. 获取已完成的任务
        success, completed_tasks = self.get_completed_tasks()
        if not success or not completed_tasks:
            print("没有找到已完成的任务")
            return

        # 2. 遍历每个已完成的任务，查询漏洞信息
        all_principle_vulns = []

        for task in completed_tasks:
            task_id = task.get('task_id')
            task_name = task.get('task_name', '未知任务')

            print(f"\n正在分析任务: {task_name} (ID: {task_id})")

            # 获取任务的漏洞分布信息
            success, vuln_data = self.get_vuln_distribution(task_id)
            if not success:
                print(f"  跳过任务 {task_id}，无法获取漏洞信息")
                continue

            # 提取漏洞列表
            vuln_info = vuln_data.get('data', {}).get('vulns_info', {})
            vuln_list = vuln_info.get('vuln_distribution', {}).get('vuln_list', [])

            if not vuln_list:
                print(f"  任务 {task_id} 没有发现漏洞")
                continue

            # 过滤原理扫描漏洞
            principle_vulns = self.filter_principle_scan_vulns(vuln_list)

            if principle_vulns:
                print(f"  ✓ 发现 {len(principle_vulns)} 个原理扫描漏洞")
                all_principle_vulns.extend(principle_vulns)

                # 显示原理扫描漏洞详情
                for vuln in principle_vulns:
                    vuln_name = vuln.get('i18n_name', '未知漏洞')
                    vuln_level = vuln.get('vuln_level', '未知')
                    target = vuln.get('target', '未知目标')
                    severity_points = vuln.get('severity_points', 0)

                    print(f"    - {vuln_name}")
                    print(f"      风险等级: {vuln_level}, 评分: {severity_points}, 目标: {target}")
            else:
                print(f"  任务 {task_id} 没有发现原理扫描漏洞")

        # 3. 汇总统计
        print(f"\n" + "=" * 60)
        print("原理扫描漏洞汇总统计")
        print("=" * 60)
        print(f"总计发现原理扫描漏洞: {len(all_principle_vulns)} 个")

        if all_principle_vulns:
            # 按风险等级统计
            level_count = {}
            for vuln in all_principle_vulns:
                level = vuln.get('vuln_level', '未知')
                level_count[level] = level_count.get(level, 0) + 1

            print("按风险等级统计:")
            for level, count in level_count.items():
                print(f"  {level}: {count} 个")

        return all_principle_vulns

def main():
    """主函数"""
    # 创建登录实例
    login_client = GreenLeagueLogin()

    # 执行自动登录
    success, result = login_client.auto_login()

    if success:
        print("\n自动登录流程完成！")

        # 登录成功后，分析已完成任务的原理扫描漏洞
        try:
            login_client.analyze_completed_tasks_vulns()
        except Exception as e:
            print(f"分析漏洞信息时发生错误: {str(e)}")
    else:
        print("\n自动登录流程失败！")

if __name__ == "__main__":
    main()
