#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟漏洞分析示例
演示如何使用扩展功能查询已完成任务的原理扫描漏洞
"""

from auto_login import GreenLeagueLogin

def example_full_analysis():
    """完整的漏洞分析示例"""
    print("=== 完整的漏洞分析示例 ===")
    
    # 创建登录客户端
    client = GreenLeagueLogin(host="************")
    
    # 1. 自动登录
    print("步骤1: 执行自动登录...")
    success, result = client.auto_login()
    
    if not success:
        print("登录失败，无法继续分析")
        return
    
    print("登录成功，开始分析漏洞信息...")
    
    # 2. 分析已完成任务的原理扫描漏洞
    try:
        principle_vulns = client.analyze_completed_tasks_vulns()
        
        if principle_vulns:
            print(f"\n发现 {len(principle_vulns)} 个原理扫描漏洞")
            
            # 可以在这里添加更多的分析逻辑
            # 例如：导出到文件、发送报告等
            
        else:
            print("\n没有发现原理扫描漏洞")
            
    except Exception as e:
        print(f"分析过程中发生错误: {str(e)}")

def example_step_by_step_analysis():
    """分步骤的漏洞分析示例"""
    print("=== 分步骤的漏洞分析示例 ===")
    
    client = GreenLeagueLogin(host="************")
    
    # 1. 登录
    success, result = client.auto_login()
    if not success:
        print("登录失败")
        return
    
    # 2. 获取已完成的任务
    print("\n步骤2: 获取已完成的任务...")
    success, completed_tasks = client.get_completed_tasks()
    
    if not success or not completed_tasks:
        print("没有找到已完成的任务")
        return
    
    # 3. 选择第一个任务进行详细分析
    first_task = completed_tasks[0]
    task_id = first_task.get('task_id')
    task_name = first_task.get('task_name', '未知任务')
    
    print(f"\n步骤3: 分析任务 '{task_name}' (ID: {task_id}) 的漏洞信息...")
    
    # 4. 获取漏洞分布信息
    success, vuln_data = client.get_vuln_distribution(task_id)
    
    if not success:
        print(f"无法获取任务 {task_id} 的漏洞信息")
        return
    
    # 5. 提取和分析漏洞
    vuln_info = vuln_data.get('data', {}).get('vulns_info', {})
    vuln_list = vuln_info.get('vuln_distribution', {}).get('vuln_list', [])
    
    print(f"任务 {task_id} 共有 {len(vuln_list)} 个漏洞")
    
    # 6. 过滤原理扫描漏洞
    principle_vulns = client.filter_principle_scan_vulns(vuln_list)
    
    print(f"其中原理扫描漏洞有 {len(principle_vulns)} 个")
    
    # 7. 显示详细信息
    if principle_vulns:
        print("\n原理扫描漏洞详情:")
        for i, vuln in enumerate(principle_vulns, 1):
            vuln_name = vuln.get('i18n_name', '未知漏洞')
            vuln_level = vuln.get('vuln_level', '未知')
            target = vuln.get('target', '未知目标')
            severity_points = vuln.get('severity_points', 0)
            
            print(f"{i}. {vuln_name}")
            print(f"   风险等级: {vuln_level}")
            print(f"   评分: {severity_points}")
            print(f"   目标: {target}")
            print()

def example_custom_task_analysis():
    """自定义任务分析示例"""
    print("=== 自定义任务分析示例 ===")
    
    client = GreenLeagueLogin(host="************")
    
    # 登录
    success, result = client.auto_login()
    if not success:
        print("登录失败")
        return
    
    # 获取所有任务
    success, task_data = client.get_task_list(page=1, page_size=50)
    if not success:
        print("获取任务列表失败")
        return
    
    all_tasks = task_data.get('data', {}).get('results', [])
    
    print(f"共找到 {len(all_tasks)} 个任务")
    print("\n任务列表:")
    
    for i, task in enumerate(all_tasks, 1):
        task_id = task.get('task_id')
        task_name = task.get('task_name', '未知任务')
        task_status = task.get('task_status')
        create_time = task.get('create_time', '未知时间')
        
        status_text = "已完成" if task_status == 15 else f"状态{task_status}"
        
        print(f"{i}. 任务ID: {task_id}")
        print(f"   任务名称: {task_name}")
        print(f"   状态: {status_text}")
        print(f"   创建时间: {create_time}")
        print()
    
    # 可以在这里添加用户交互，让用户选择要分析的任务

if __name__ == "__main__":
    # 运行完整分析示例
    example_full_analysis()
    
    print("\n" + "="*80 + "\n")
    
    # 运行分步骤分析示例
    # example_step_by_step_analysis()
    
    # 运行自定义任务分析示例
    # example_custom_task_analysis()
